<template>
  <div>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="门店" prop="shopId">
        <el-select
          v-model="queryParams.shopId"
          placeholder="请选择门店"
          clearable
          class="!w-240px"
        >
          <!-- 门店选项将通过API获取 -->
          <el-option
            v-for="shop in shopOptions"
            :key="shop.id"
            :label="shop.name"
            :value="shop.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="桌面分类" prop="cateId">
        <el-select
          v-model="queryParams.cateId"
          placeholder="请选择桌面分类"
          clearable
          class="!w-240px"
        >
          <!-- 桌面分类选项将通过API获取 -->
          <el-option
            v-for="cate in cateOptions"
            :key="cate.id"
            :label="cate.name"
            :value="cate.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="手机号" prop="mobile">
        <el-input
          v-model="queryParams.mobile"
          placeholder="请输入手机号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="排队状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择排队状态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in statusOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          value-format="YYYY-MM-DD"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['desk:queue:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['desk:queue:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <el-table-column label="排队号码" align="center" prop="queueNumber" />
      <el-table-column label="门店名称" align="center" prop="shopName" />
      <el-table-column label="桌面分类" align="center" prop="cateName" />
      <el-table-column label="用户名称" align="center" prop="nickname" />
      <el-table-column label="手机号" align="center" prop="mobile" />
      <el-table-column label="就餐人数" align="center" prop="peopleCount" />
      <el-table-column label="排队状态" align="center" prop="status">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusLabel(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="等待时间" align="center">
        <template #default="scope">
          <span>{{ calculateWaitTime(scope.row) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" :formatter="dateFormatter" />
      <el-table-column label="操作" align="center" width="250">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['desk:queue:update']"
            v-if="scope.row.status === 1 || scope.row.status === 2"
          >
            编辑
          </el-button>
          <el-button
            link
            type="warning"
            @click="handleCallNumber(scope.row.id)"
            v-hasPermi="['desk:queue:call']"
            v-if="scope.row.status === 1"
          >
            叫号
          </el-button>
          <el-button
            link
            type="success"
            @click="handleSeatNumber(scope.row.id)"
            v-hasPermi="['desk:queue:seat']"
            v-if="scope.row.status === 2"
          >
            入座
          </el-button>
          <el-button
            link
            type="info"
            @click="handlePassNumber(scope.row.id)"
            v-hasPermi="['desk:queue:pass']"
            v-if="scope.row.status === 2"
          >
            过号
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['desk:queue:delete']"
          >
            删除
          </el-button>
          <el-button
            link
            type="primary"
            @click="viewDetail(scope.row.id)"
            v-hasPermi="['desk:queue:query']"
          >
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <!-- <ShopDeskQueueForm ref="formRef" @success="getList" /> -->

  <!-- 详情弹窗 -->
  <!-- <ShopDeskQueueDetail ref="detailRef" /> -->

  <!-- 统计分析弹窗 -->
  <!-- <ShopDeskQueueStats ref="statsRef" /> -->

  <!-- 设置弹窗 -->
  <!-- <ShopDeskQueueSettings ref="settingsRef" /> -->

  <!-- 导出弹窗 -->
  <!-- <ShopDeskQueueExport ref="exportRef" /> -->
  </div>
</template>

<script setup lang="ts" name="ShopDeskQueue">
import { ref, reactive, onMounted } from 'vue'

// 简单的日期格式化函数
const dateFormatter = (row: any, column: any, cellValue: string) => {
  if (!cellValue) return ''
  return new Date(cellValue).toLocaleString()
}

// 定义排队记录类型
interface QueueItem {
  id: number
  queueNumber: string
  shopName?: string
  cateName?: string
  nickname?: string
  mobile?: string
  peopleCount?: number
  status?: number
  createTime?: string
  callTime?: string
  seatTime?: string
}

// 模拟API调用 - 实际项目中应该导入真实的API
const ShopDeskQueueApi = {
  getShopDeskQueuePage: async (params: any) => {
    // 模拟API调用
    console.log('获取排队列表', params)
    return { list: [], total: 0 }
  },
  callNumber: async (id: number) => {
    // 模拟叫号API调用
    console.log('叫号', id)
    return { success: true }
  },
  seatNumber: async (id: number) => {
    // 模拟入座API调用
    console.log('入座', id)
    return { success: true }
  },
  passNumber: async (id: number) => {
    // 模拟过号API调用
    console.log('过号', id)
    return { success: true }
  },
  deleteShopDeskQueue: async (id: number) => {
    // 模拟删除API调用
    console.log('删除', id)
    return { success: true }
  }
}

// 模拟消息提示
const message = {
  success: (msg: string) => console.log('成功:', msg),
  error: (msg: string) => console.error('错误:', msg),
  warning: (msg: string) => console.warn('警告:', msg),
  confirm: (msg: string) => Promise.resolve(true),
  delConfirm: () => Promise.resolve(true)
}

// 模拟国际化
const { t } = { t: (key: string) => key }

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref<QueueItem[]>([]) // 列表的数据
const dateRange = ref([]) // 日期范围
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  shopId: undefined as number | undefined,
  cateId: undefined as number | undefined,
  uid: undefined as number | undefined,
  nickname: undefined as string | undefined,
  mobile: undefined as string | undefined,
  peopleCount: undefined as number | undefined,
  status: undefined as number | undefined,
  createTimeBegin: undefined as string | undefined,
  createTimeEnd: undefined as string | undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

// 门店选项
const shopOptions = ref([
  { id: 1, name: '总店' },
  { id: 2, name: '北京分店' },
  { id: 3, name: '上海分店' }
])

// 桌面分类选项
const cateOptions = ref([
  { id: 1, name: '大桌' },
  { id: 2, name: '中桌' },
  { id: 3, name: '小桌' },
  { id: 4, name: 'VIP包间' }
])

// 排队状态选项
const statusOptions = [
  { label: '排队中', value: 1 },
  { label: '已叫号', value: 2 },
  { label: '已取消', value: 3 },
  { label: '已过号', value: 4 },
  { label: '已入座', value: 5 }
]

// 获取状态标签
const getStatusLabel = (status: number) => {
  const option = statusOptions.find(item => item.value === status)
  return option ? option.label : '未知'
}

// 获取状态类型
const getStatusType = (status: number) => {
  switch (status) {
    case 1: return 'info'     // 排队中
    case 2: return 'warning'  // 已叫号
    case 3: return 'danger'   // 已取消
    case 4: return 'danger'   // 已过号
    case 5: return 'success'  // 已入座
    default: return 'info'
  }
}

// 计算等待时间
const calculateWaitTime = (row: QueueItem) => {
  if (!row.createTime) return '未知'

  const createTime = new Date(row.createTime)
  const now = new Date()

  // 如果已入座，计算从创建到入座的时间
  if (row.status === 2 && row.seatTime) {
    const seatTime = new Date(row.seatTime)
    const waitMs = seatTime.getTime() - createTime.getTime()
    return formatWaitTime(waitMs)
  }

  // 如果已叫号但未入座，计算从创建到叫号的时间
  if (row.status === 1 && row.callTime) {
    const callTime = new Date(row.callTime)
    const waitMs = callTime.getTime() - createTime.getTime()
    return formatWaitTime(waitMs)
  }

  // 如果还在等待中，计算从创建到现在的时间
  if (row.status === 0) {
    const waitMs = now.getTime() - createTime.getTime()
    return formatWaitTime(waitMs)
  }

  return '未知'
}

// 格式化等待时间
const formatWaitTime = (waitMs: number) => {
  const minutes = Math.floor(waitMs / (1000 * 60))
  if (minutes < 60) {
    return `${minutes}分钟`
  } else {
    const hours = Math.floor(minutes / 60)
    const remainMinutes = minutes % 60
    return `${hours}小时${remainMinutes}分钟`
  }
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    // 处理日期范围
    if (dateRange.value && dateRange.value.length === 2) {
      queryParams.createTimeBegin = dateRange.value[0] + ' 00:00:00'
      queryParams.createTimeEnd = dateRange.value[1] + ' 23:59:59'
    } else {
      queryParams.createTimeBegin = undefined
      queryParams.createTimeEnd = undefined
    }
    
    const res = await ShopDeskQueueApi.getShopDeskQueuePage(queryParams)
    list.value = res.list
    total.value = res.total
  } catch (error) {
    console.error('获取排队叫号列表失败:', error)
    message.error('获取排队叫号列表失败')
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  dateRange.value = []
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 查看详情操作 */
const detailRef = ref()
const viewDetail = (id: number) => {
  if (!id) {
    message.error('无效的记录ID')
    return
  }
  if (!detailRef.value) {
    message.error('详情组件未加载')
    return
  }
  detailRef.value.open(id)
}

/** 语音播报功能 */
const playVoiceAnnouncement = (queueNumber: string) => {
  // 检查浏览器是否支持语音合成
  if ('speechSynthesis' in window) {
    try {
      // 停止当前正在播放的语音
      window.speechSynthesis.cancel()

      // 创建语音播报内容
      const text = `请${queueNumber}号就餐`
      const utterance = new SpeechSynthesisUtterance(text)

      // 设置语音参数
      utterance.lang = 'zh-CN' // 中文
      utterance.rate = 0.8 // 语速稍慢一些，更清晰
      utterance.pitch = 1 // 音调
      utterance.volume = 1 // 音量

      // 播放语音
      window.speechSynthesis.speak(utterance)

      console.log('语音播报:', text)
    } catch (error) {
      console.error('语音播报失败:', error)
      message.warning('语音播报失败，请检查浏览器设置')
    }
  } else {
    console.warn('当前浏览器不支持语音合成功能')
    message.warning('当前浏览器不支持语音播报功能')
  }
}

/** 叫号操作 */
const handleCallNumber = async (id: number) => {
  try {
    await message.confirm('确认要叫号吗？')

    // 先获取当前排队记录信息，用于语音播报
    const currentQueue = list.value.find(item => item.id === id)

    await ShopDeskQueueApi.callNumber(id)
    message.success('叫号成功')

    // 语音播报: 请xx号就餐
    if (currentQueue && currentQueue.queueNumber) {
      playVoiceAnnouncement(currentQueue.queueNumber)
    }

    await getList()
  } catch (error) {
    console.error('叫号失败:', error)
  }
}

/** 入座操作 */
const handleSeatNumber = async (id: number) => {
  try {
    await message.confirm('确认客人已入座吗？')
    await ShopDeskQueueApi.seatNumber(id)
    message.success('入座成功')
    await getList()
  } catch (error) {
    console.error('入座操作失败:', error)
  }
}

/** 过号操作 */
const handlePassNumber = async (id: number) => {
  try {
    await message.confirm('确认要将此号码标记为过号吗？')
    await ShopDeskQueueApi.passNumber(id)
    message.success('过号成功')
    await getList()
  } catch (error) {
    console.error('过号操作失败:', error)
  }
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ShopDeskQueueApi.deleteShopDeskQueue(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch (error) {
    console.error('删除失败:', error)
  }
}

/** 导出按钮操作 */
const exportRef = ref()
const handleExport = () => {
  if (!exportRef.value) return
  exportRef.value.open()
}

/** 统计分析 */
const statsRef = ref()
const openStats = () => {
  statsRef.value.open()
}

/** 设置 */
const settingsRef = ref()
const openSettings = () => {
  settingsRef.value.open()
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
