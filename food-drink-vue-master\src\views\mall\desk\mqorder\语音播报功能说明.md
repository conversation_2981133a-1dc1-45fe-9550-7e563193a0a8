# 叫号语音播报功能实现说明

## 功能概述

在排队叫号系统中实现了浏览器端语音播报功能，当点击"叫号"按钮时，系统会自动使用浏览器的语音合成API播报叫号信息。

## 实现特性

### 🔊 核心功能
- **自动语音播报**：点击叫号按钮后自动播报"请XX号就餐"
- **中文语音合成**：使用中文语音引擎，发音清晰
- **智能参数优化**：语速、音调、音量经过优化，确保播报效果
- **错误处理**：完善的错误处理和用户提示

### ⚙️ 技术参数
- **语言**：zh-CN (中文)
- **语速**：0.8 (稍慢，更清晰)
- **音调**：1.0 (标准音调)
- **音量**：1.0 (最大音量)

### 🛡️ 兼容性处理
- **浏览器检测**：自动检测浏览器是否支持语音合成
- **优雅降级**：不支持时显示友好提示，不影响正常功能
- **冲突处理**：自动停止之前的播报，避免重叠

## 代码实现

### 语音播报核心函数

```typescript
const playVoiceAnnouncement = (queueNumber: string) => {
  // 检查浏览器是否支持语音合成
  if ('speechSynthesis' in window) {
    try {
      // 停止当前正在播放的语音
      window.speechSynthesis.cancel()
      
      // 创建语音播报内容
      const text = `请${queueNumber}号就餐`
      const utterance = new SpeechSynthesisUtterance(text)
      
      // 设置语音参数
      utterance.lang = 'zh-CN' // 中文
      utterance.rate = 0.8 // 语速稍慢一些，更清晰
      utterance.pitch = 1 // 音调
      utterance.volume = 1 // 音量
      
      // 播放语音
      window.speechSynthesis.speak(utterance)
      
      console.log('语音播报:', text)
    } catch (error) {
      console.error('语音播报失败:', error)
      message.warning('语音播报失败，请检查浏览器设置')
    }
  } else {
    console.warn('当前浏览器不支持语音合成功能')
    message.warning('当前浏览器不支持语音播报功能')
  }
}
```

### 叫号操作集成

```typescript
const handleCallNumber = async (id: number) => {
  try {
    await message.confirm('确认要叫号吗？')
    
    // 先获取当前排队记录信息，用于语音播报
    const currentQueue = list.value.find(item => item.id === id)
    
    await ShopDeskQueueApi.callNumber(id)
    message.success('叫号成功')
    
    // 语音播报: 请xx号就餐
    if (currentQueue && currentQueue.queueNumber) {
      playVoiceAnnouncement(currentQueue.queueNumber)
    }
    
    await getList()
  } catch (error) {
    console.error('叫号失败:', error)
  }
}
```

## 使用流程

1. **用户操作**：点击排队列表中的"叫号"按钮
2. **确认对话框**：系统显示确认对话框
3. **API调用**：调用后端叫号接口
4. **语音播报**：自动播报"请XX号就餐"
5. **界面更新**：刷新列表显示最新状态

## 浏览器兼容性

### ✅ 支持的浏览器
- **Chrome** 33+
- **Firefox** 49+
- **Safari** 7+
- **Edge** 14+
- **Opera** 21+

### ❌ 不支持的浏览器
- **IE** 所有版本
- **旧版本移动浏览器**

### 📱 移动端支持
- **iOS Safari** 7+
- **Android Chrome** 33+
- **微信内置浏览器** (部分支持)

## 测试工具

提供了专门的测试页面 `voice-test.html`，包含以下功能：

### 🎯 测试功能
- **基础语音测试**：测试浏览器语音合成功能
- **叫号模拟**：模拟不同号码的叫号播报
- **参数调节**：实时调整语速、音调、音量
- **兼容性检查**：检测浏览器支持情况
- **场景模拟**：模拟真实的叫号场景

### 🔧 使用方法
1. 在浏览器中打开 `voice-test.html`
2. 点击各种测试按钮验证功能
3. 调整参数找到最佳播报效果
4. 检查兼容性确保正常工作

## 注意事项

### 🔒 安全限制
- **用户交互要求**：现代浏览器要求用户先与页面交互才能播放音频
- **HTTPS要求**：某些浏览器在HTTP环境下可能限制语音功能
- **权限设置**：用户可能需要允许网站播放音频

### 🎵 音频设置
- **系统音量**：确保系统音量已开启
- **浏览器设置**：检查浏览器是否允许音频播放
- **网站权限**：确保网站有音频播放权限

### 🌐 网络环境
- **离线可用**：语音合成功能完全在本地运行，无需网络
- **响应速度**：播报响应速度取决于设备性能
- **并发处理**：自动处理多次点击，避免音频重叠

## 扩展功能建议

### 🎨 界面优化
- 添加语音播报状态指示器
- 提供音量控制滑块
- 显示播报进度条

### ⚙️ 功能增强
- 支持自定义播报内容模板
- 添加多语言支持
- 实现语音播报历史记录

### 🔧 管理功能
- 管理员可配置默认语音参数
- 支持上传自定义音频文件
- 提供语音播报统计报表

## 总结

语音播报功能已成功集成到叫号系统中，提供了良好的用户体验和完善的错误处理。该功能使用浏览器原生API实现，无需额外依赖，兼容性良好，可以有效提升排队叫号的效率和用户体验。
