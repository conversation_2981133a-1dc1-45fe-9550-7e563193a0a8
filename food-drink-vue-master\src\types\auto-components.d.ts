/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    21903: typeof import('./../api/mall/desk/mqorder/未确认 21903.crdownload')['default']
    268665: typeof import('./../api/mall/desk/mqorder/未确认 268665.crdownload')['default']
    323944: typeof import('./../api/mall/desk/mqorder/未确认 323944.crdownload')['default']
    338479: typeof import('./../api/mall/desk/mqorder/未确认 338479.crdownload')['default']
    349070: typeof import('./../api/mall/desk/mqorder/未确认 349070.crdownload')['default']
    390438: typeof import('./../api/mall/desk/mqorder/未确认 390438.crdownload')['default']
    405828: typeof import('./../api/mall/desk/mqorder/未确认 405828.crdownload')['default']
    964620: typeof import('./../api/mall/desk/mqorder/未确认 964620.crdownload')['default']
    '创建门店排队叫号_OpenAPI': typeof import('./../api/mall/desk/mqorder/创建门店排队叫号_OpenAPI.json')['default']
    '更新门店排队叫号_OpenAPI': typeof import('./../api/mall/desk/mqorder/更新门店排队叫号_OpenAPI.json')['default']
    '过号_OpenAPI': typeof import('./../api/mall/desk/mqorder/过号_OpenAPI.json')['default']
    '获得门店排队叫号_OpenAPI': typeof import('./../api/mall/desk/mqorder/获得门店排队叫号_OpenAPI.json')['default']
    '获得门店排队叫号分页_OpenAPI': typeof import('./../api/mall/desk/mqorder/获得门店排队叫号分页_OpenAPI.json')['default']
    '叫号_OpenAPI': typeof import('./../api/mall/desk/mqorder/叫号_OpenAPI.json')['default']
    '入座_OpenAPI': typeof import('./../api/mall/desk/mqorder/入座_OpenAPI.json')['default']
    '删除门店排队叫号_OpenAPI': typeof import('./../api/mall/desk/mqorder/删除门店排队叫号_OpenAPI.json')['default']
    '语音播报功能说明': typeof import('./../views/mall/desk/mqorder/语音播报功能说明.md')['default']
    AddNode: typeof import('./../components/SimpleProcessDesigner/src/addNode.vue')['default']
    AppLinkInput: typeof import('./../components/AppLinkInput/index.vue')['default']
    AppLinkSelectDialog: typeof import('./../components/AppLinkInput/AppLinkSelectDialog.vue')['default']
    Backtop: typeof import('./../components/Backtop/src/Backtop.vue')['default']
    CardTitle: typeof import('./../components/Card/src/CardTitle.vue')['default']
    CashierShift: typeof import('./../views/shift/CashierShift.vue')['default']
    ColorInput: typeof import('./../components/ColorInput/index.vue')['default']
    ConfigGlobal: typeof import('./../components/ConfigGlobal/src/ConfigGlobal.vue')['default']
    ContentDetailWrap: typeof import('./../components/ContentDetailWrap/src/ContentDetailWrap.vue')['default']
    ContentWrap: typeof import('./../components/ContentWrap/src/ContentWrap.vue')['default']
    CopperModal: typeof import('./../components/Cropper/src/CopperModal.vue')['default']
    CountTo: typeof import('./../components/CountTo/src/CountTo.vue')['default']
    Crontab: typeof import('./../components/Crontab/src/Crontab.vue')['default']
    Cropper: typeof import('./../components/Cropper/src/Cropper.vue')['default']
    CropperAvatar: typeof import('./../components/Cropper/src/CropperAvatar.vue')['default']
    Descriptions: typeof import('./../components/Descriptions/src/Descriptions.vue')['default']
    DescriptionsItemLabel: typeof import('./../components/Descriptions/src/DescriptionsItemLabel.vue')['default']
    Dialog: typeof import('./../components/Dialog/src/Dialog.vue')['default']
    DictSelect: typeof import('./../components/FormCreate/src/components/DictSelect.vue')['default']
    DictTag: typeof import('./../components/DictTag/src/DictTag.vue')['default']
    DocAlert: typeof import('./../components/DocAlert/index.vue')['default']
    Draggable: typeof import('./../components/Draggable/index.vue')['default']
    Echart: typeof import('./../components/Echart/src/Echart.vue')['default']
    Editor: typeof import('./../components/Editor/src/Editor.vue')['default']
    EditorMaterials: typeof import('./../components/Materials/src/editorMaterials.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAside: typeof import('element-plus/es')['ElAside']
    ElAutoResizer: typeof import('element-plus/es')['ElAutoResizer']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapseTransition: typeof import('element-plus/es')['ElCollapseTransition']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElImageViewer: typeof import('element-plus/es')['ElImageViewer']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSkeleton: typeof import('element-plus/es')['ElSkeleton']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElSpace: typeof import('element-plus/es')['ElSpace']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTableV2: typeof import('element-plus/es')['ElTableV2']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElText: typeof import('element-plus/es')['ElText']
    ElTimeline: typeof import('element-plus/es')['ElTimeline']
    ElTimelineItem: typeof import('element-plus/es')['ElTimelineItem']
    ElTimePicker: typeof import('element-plus/es')['ElTimePicker']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElTreeSelect: typeof import('element-plus/es')['ElTreeSelect']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    Error: typeof import('./../components/Error/src/Error.vue')['default']
    Form: typeof import('./../components/Form/src/Form.vue')['default']
    Highlight: typeof import('./../components/Highlight/src/Highlight.vue')['default']
    Icon: typeof import('./../components/Icon/src/Icon.vue')['default']
    IconSelect: typeof import('./../components/Icon/src/IconSelect.vue')['default']
    IFrame: typeof import('./../components/IFrame/src/IFrame.vue')['default']
    ImageViewer: typeof import('./../components/ImageViewer/src/ImageViewer.vue')['default']
    Infotip: typeof import('./../components/Infotip/src/Infotip.vue')['default']
    InputPassword: typeof import('./../components/InputPassword/src/InputPassword.vue')['default']
    InputWithColor: typeof import('./../components/InputWithColor/index.vue')['default']
    MagicCubeEditor: typeof import('./../components/MagicCubeEditor/index.vue')['default']
    Materials: typeof import('./../components/Materials/src/Materials.vue')['default']
    Mqorder: typeof import('./../views/mall/desk/mqorder/index.vue')['default']
    NodeWrap: typeof import('./../components/SimpleProcessDesigner/src/nodeWrap.vue')['default']
    OperateLogV2: typeof import('./../components/OperateLogV2/src/OperateLogV2.vue')['default']
    Pagination: typeof import('./../components/Pagination/index.vue')['default']
    Qrcode: typeof import('./../components/Qrcode/src/Qrcode.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterSearch: typeof import('./../components/RouterSearch/index.vue')['default']
    RouterView: typeof import('vue-router')['RouterView']
    Search: typeof import('./../components/Search/src/Search.vue')['default']
    ShopDeskQueueDetail: typeof import('./../views/mall/desk/mqorder/ShopDeskQueueDetail.vue')['default']
    ShopDeskQueueExport: typeof import('./../views/mall/desk/mqorder/ShopDeskQueueExport.vue')['default']
    ShopDeskQueueForm: typeof import('./../views/mall/desk/mqorder/ShopDeskQueueForm.vue')['default']
    ShopDeskQueueSettings: typeof import('./../views/mall/desk/mqorder/ShopDeskQueueSettings.vue')['default']
    ShopDeskQueueStats: typeof import('./../views/mall/desk/mqorder/ShopDeskQueueStats.vue')['default']
    ShortcutDateRangePicker: typeof import('./../components/ShortcutDateRangePicker/index.vue')['default']
    Sticky: typeof import('./../components/Sticky/src/Sticky.vue')['default']
    SummaryCard: typeof import('./../components/SummaryCard/index.vue')['default']
    Table: typeof import('./../components/Table/src/Table.vue')['default']
    TableSelectForm: typeof import('./../components/Table/src/TableSelectForm.vue')['default']
    Tooltip: typeof import('./../components/Tooltip/src/Tooltip.vue')['default']
    UploadFile: typeof import('./../components/UploadFile/src/UploadFile.vue')['default']
    UploadImg: typeof import('./../components/UploadFile/src/UploadImg.vue')['default']
    UploadImgs: typeof import('./../components/UploadFile/src/UploadImgs.vue')['default']
    Verify: typeof import('./../components/Verifition/src/Verify.vue')['default']
    VerifyPoints: typeof import('./../components/Verifition/src/Verify/VerifyPoints.vue')['default']
    VerifySlide: typeof import('./../components/Verifition/src/Verify/VerifySlide.vue')['default']
    VerticalButtonGroup: typeof import('./../components/VerticalButtonGroup/index.vue')['default']
    VoiceTest: typeof import('./../views/mall/desk/mqorder/voice-test.html')['default']
    XButton: typeof import('./../components/XButton/src/XButton.vue')['default']
    XTextButton: typeof import('./../components/XButton/src/XTextButton.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
