<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语音播报测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background-color: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background-color: #337ecc;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .warning {
            background-color: #e6a23c;
        }
        .warning:hover {
            background-color: #cf9236;
        }
        .success {
            background-color: #67c23a;
        }
        .success:hover {
            background-color: #5daf34;
        }
        .info {
            padding: 15px;
            background-color: #e1f3ff;
            border-left: 4px solid #409eff;
            margin: 15px 0;
        }
        .controls {
            margin: 15px 0;
        }
        label {
            display: inline-block;
            width: 80px;
            margin-right: 10px;
        }
        input, select {
            padding: 5px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        .queue-demo {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }
        .queue-item {
            background: #f0f9ff;
            border: 1px solid #b3d8ff;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            min-width: 100px;
        }
        .queue-item.calling {
            background: #fff7e6;
            border-color: #ffc069;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔊 叫号语音播报测试</h1>
        
        <div class="info">
            <strong>说明：</strong>此页面用于测试浏览器语音合成功能，确保叫号语音播报正常工作。
            请确保浏览器允许音频播放，并调整合适的音量。
        </div>

        <div class="test-section">
            <h3>🎯 基础语音测试</h3>
            <button onclick="testBasicVoice()">测试基础语音</button>
            <button onclick="testQueueCall('A001')">测试叫号 A001</button>
            <button onclick="testQueueCall('B123')">测试叫号 B123</button>
            <button onclick="testQueueCall('80626001')">测试叫号 80626001</button>
        </div>

        <div class="test-section">
            <h3>⚙️ 语音参数调节</h3>
            <div class="controls">
                <label>语速:</label>
                <input type="range" id="rate" min="0.5" max="2" step="0.1" value="0.8">
                <span id="rateValue">0.8</span>
                
                <label>音调:</label>
                <input type="range" id="pitch" min="0.5" max="2" step="0.1" value="1">
                <span id="pitchValue">1</span>
                
                <label>音量:</label>
                <input type="range" id="volume" min="0" max="1" step="0.1" value="1">
                <span id="volumeValue">1</span>
            </div>
            <button onclick="testCustomVoice()">使用自定义参数测试</button>
        </div>

        <div class="test-section">
            <h3>🎭 模拟叫号场景</h3>
            <div class="queue-demo" id="queueDemo">
                <!-- 动态生成排队号码 -->
            </div>
            <button onclick="generateQueue()">生成模拟排队</button>
            <button onclick="callNextNumber()" class="warning">叫下一号</button>
            <button onclick="stopVoice()" class="success">停止播报</button>
        </div>

        <div class="test-section">
            <h3>🔧 浏览器兼容性检查</h3>
            <div id="compatibilityInfo"></div>
            <button onclick="checkCompatibility()">检查兼容性</button>
        </div>
    </div>

    <script>
        // 语音播报功能
        function playVoiceAnnouncement(queueNumber, customSettings = {}) {
            // 检查浏览器是否支持语音合成
            if ('speechSynthesis' in window) {
                try {
                    // 停止当前正在播放的语音
                    window.speechSynthesis.cancel()
                    
                    // 创建语音播报内容
                    const text = `请${queueNumber}号就餐`
                    const utterance = new SpeechSynthesisUtterance(text)
                    
                    // 设置语音参数
                    utterance.lang = 'zh-CN' // 中文
                    utterance.rate = customSettings.rate || 0.8 // 语速
                    utterance.pitch = customSettings.pitch || 1 // 音调
                    utterance.volume = customSettings.volume || 1 // 音量
                    
                    // 播放语音
                    window.speechSynthesis.speak(utterance)
                    
                    console.log('语音播报:', text)
                    return true
                } catch (error) {
                    console.error('语音播报失败:', error)
                    alert('语音播报失败，请检查浏览器设置')
                    return false
                }
            } else {
                console.warn('当前浏览器不支持语音合成功能')
                alert('当前浏览器不支持语音播报功能')
                return false
            }
        }

        // 测试基础语音
        function testBasicVoice() {
            const utterance = new SpeechSynthesisUtterance('语音播报测试成功')
            utterance.lang = 'zh-CN'
            window.speechSynthesis.speak(utterance)
        }

        // 测试叫号
        function testQueueCall(number) {
            playVoiceAnnouncement(number)
        }

        // 测试自定义参数
        function testCustomVoice() {
            const rate = parseFloat(document.getElementById('rate').value)
            const pitch = parseFloat(document.getElementById('pitch').value)
            const volume = parseFloat(document.getElementById('volume').value)
            
            playVoiceAnnouncement('A001', { rate, pitch, volume })
        }

        // 停止语音
        function stopVoice() {
            window.speechSynthesis.cancel()
        }

        // 更新参数显示
        document.getElementById('rate').addEventListener('input', function() {
            document.getElementById('rateValue').textContent = this.value
        })
        document.getElementById('pitch').addEventListener('input', function() {
            document.getElementById('pitchValue').textContent = this.value
        })
        document.getElementById('volume').addEventListener('input', function() {
            document.getElementById('volumeValue').textContent = this.value
        })

        // 模拟排队数据
        let queueData = []
        let currentIndex = 0

        // 生成模拟排队
        function generateQueue() {
            queueData = [
                'A001', 'A002', 'A003', 'B101', 'B102', 'C201', 'C202', 'D301'
            ]
            currentIndex = 0
            renderQueue()
        }

        // 渲染排队列表
        function renderQueue() {
            const container = document.getElementById('queueDemo')
            container.innerHTML = queueData.map((number, index) => {
                const isCalling = index === currentIndex
                return `<div class="queue-item ${isCalling ? 'calling' : ''}">${number}${isCalling ? ' (叫号中)' : ''}</div>`
            }).join('')
        }

        // 叫下一号
        function callNextNumber() {
            if (currentIndex < queueData.length) {
                const number = queueData[currentIndex]
                playVoiceAnnouncement(number)
                renderQueue()
                currentIndex++
            } else {
                alert('所有号码已叫完')
            }
        }

        // 检查浏览器兼容性
        function checkCompatibility() {
            const info = document.getElementById('compatibilityInfo')
            let html = '<h4>兼容性检查结果：</h4><ul>'
            
            // 检查语音合成支持
            if ('speechSynthesis' in window) {
                html += '<li>✅ 支持语音合成 (SpeechSynthesis)</li>'
            } else {
                html += '<li>❌ 不支持语音合成</li>'
            }
            
            // 检查语音识别支持
            if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                html += '<li>✅ 支持语音识别</li>'
            } else {
                html += '<li>❌ 不支持语音识别</li>'
            }
            
            // 检查音频上下文
            if ('AudioContext' in window || 'webkitAudioContext' in window) {
                html += '<li>✅ 支持音频上下文</li>'
            } else {
                html += '<li>❌ 不支持音频上下文</li>'
            }
            
            // 浏览器信息
            html += `<li>浏览器: ${navigator.userAgent}</li>`
            html += `<li>语言: ${navigator.language}</li>`
            
            html += '</ul>'
            info.innerHTML = html
        }

        // 页面加载时初始化
        window.onload = function() {
            generateQueue()
            checkCompatibility()
        }
    </script>
</body>
</html>
